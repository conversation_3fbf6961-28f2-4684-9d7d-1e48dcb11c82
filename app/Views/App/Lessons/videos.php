<style>
    #playlist {
        padding: 20px;
        overflow-y: auto;
        background-color: #ffffff;
        border-right: 1px solid #ccc;
        border-radius: 10px;
    }

    @media screen and (min-width: 750px) {
        #playlist {
            height: 70vh;
        }
    }

    #playlist ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    #playlist li {
        padding: 10px;
        cursor: pointer;
        border-bottom: 1px solid #ddd;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    #playlist li span.lesson-count {
        font-weight: bold;
        color: #000;
        margin-right: 10px;
        /* Spacing between count and text */
    }

    #playlist li.active,
    #playlist li:hover {
        background-color: #2CAFAE;
        /* Subtle red color for hover/active state */
        color: white;
    }

    #playlist li i {
        margin-left: 10px;
        /* Space between text and icon on the right */
        color: #888;
        /* Icon color */
    }

    #video-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
    }

    .iframe-container {
        position: relative;
        overflow: hidden;
        width: 100%;
        padding-top: 56.25%;
    }

    .iframe-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
        max-height: 500px;
    }

    @media screen and (max-width: 750px) {
        #video-container {
            width: 100%;
        }

        .iframe-container {
            padding-bottom: 56.25%;
        }
    }

    /* Custom scrollbar styles */
    #playlist::-webkit-scrollbar {
        width: 5px;
        /* Width of the scrollbar */
    }

    #playlist::-webkit-scrollbar-track {
        background: #f1f1f1;
        /* Background of the track */
    }

    #playlist::-webkit-scrollbar-thumb {
        background: #888;
        /* Color of the scrollbar thumb */
        border-radius: 4px;
        /* Rounded corners for the thumb */
    }

    #playlist::-webkit-scrollbar-thumb:hover {
        background: #555;
        /* Darker color on hover */
    }

    /* Comments Section Styles */
    .comments-section {
        margin-top: 30px;
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 10px;
    }

    .comment-form {
        margin-bottom: 30px;
    }

    .comment-form textarea {
        width: 100%;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #ddd;
        resize: vertical;
        min-height: 100px;
    }

    .comment-form button {
        background-color: #2CAFAE;
        color: white;
        border: none;
        padding: 8px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 10px;
    }

    .comment-form button:hover {
        background-color: #249c9b;
    }

    .comments-list {
        margin-top: 20px;
    }

    .comment {
        padding: 15px;
        margin-bottom: 15px;
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .comment-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-size: 0.9em;
        color: #666;
    }

    .comment-author {
        font-weight: bold;
        color: #333;
    }

    .comment-date {
        color: #999;
    }

    .comment-content {
        line-height: 1.5;
    }

    .no-comments {
        color: #999;
        text-align: center;
        padding: 20px;
    }

.contact-overlay-marquee {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9999;
    overflow: hidden;
}

.contact-overlay-marquee p {
    position: absolute;
    font-size: 16px;
    font-family: Arial, sans-serif;
    color: red;
    padding: 8px 16px;
    border-radius: 8px;
    white-space: nowrap;
    animation: shuffle-marquee 32s linear infinite;
}

/* Keyframe sequence to move text in 4 directions */
@keyframes shuffle-marquee {
    0% {
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
    }
    25% {
        left: -100%;
        top: 50%;
        transform: translateY(-50%);
    }

    25.01% {
        top: -100%;
        left: 50%;
        transform: translateX(-50%);
    }
    50% {
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
    }

    50.01% {
        left: -100%;
        top: 75%;
        transform: translateY(-50%);
    }
    75% {
        left: 100%;
        top: 75%;
        transform: translateY(-50%);
    }

    75.01% {
        top: 100%;
        left: 30%;
        transform: translateX(-50%);
    }
    100% {
        top: -100%;
        left: 30%;
        transform: translateX(-50%);
    }
}
</style>
<style>
    .video-container {
        position: relative;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }
    
    .video-player {
        width: 100%;
        height: auto;
        display: block;
    }
    
    .watermark {
        position: absolute;
        top: 35px;
        left: 35px;
        background: rgba(255,255,255,0.2);
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        color:rgb(165, 165, 165);
        pointer-events: none;
        z-index: 10;
        transition: all 3s ease-in-out;
    }
    
    .custom-controls {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0,0,0,0.8));
        color: white;
        padding: 15px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1000;
        pointer-events: auto;
    }
    
    .video-container:hover .custom-controls,
    .video-container.paused .custom-controls {
        opacity: 1;
    }
    
    .progress-container {
        width: 100%;
        height: 6px;
        background: rgba(255,255,255,0.3);
        border-radius: 3px;
        margin-bottom: 10px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .progress-bar {
        height: 100%;
        background: #007bff;
        border-radius: 3px;
        transition: width 0.1s ease;
        position: relative;
        z-index: 3;
    }
    
    .progress-buffer {
        height: 100%;
        background: rgba(255,255,255,0.4);
        border-radius: 3px;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        pointer-events: none;
    }
    
    .control-btn {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        margin: 0 5px;
        cursor: pointer;
        transition: color 0.2s ease;
        padding: 8px;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        min-height: 40px;
    }

    .control-btn:hover {
        color: #007bff;
        background: rgba(255, 255, 255, 0.1);
    }

    .control-btn:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }

    .control-btn i {
        pointer-events: none;
    }
    
    .time-display {
        font-size: 14px;
        margin: 0 10px;
    }
    
    .quality-selector {
        background: rgba(0,0,0,0.8);
        border: 1px solid #555;
        color: white;
        border-radius: 4px;
        padding: 2px 8px;
    }
    
    .loading-spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 2rem;
        display: none;
        z-index: 1000;
        background: rgba(0, 0, 0, 0.7);
        padding: 20px;
        border-radius: 8px;
        text-align: center;
    }

    .loading-spinner.show {
        display: block;
    }

    .play-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(100% - 80px); /* Exclude controls area (approximately 80px) */
        background: rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 999;
        cursor: pointer;
        pointer-events: auto;
    }

    .large-play-btn {
        background: rgba(0, 0, 0, 0.8);
        border: 3px solid white;
        border-radius: 50%;
        width: 80px;
        height: 80px;
        color: white;
        font-size: 24px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .large-play-btn:hover {
        background: rgba(0, 0, 0, 0.9);
        transform: scale(1.1);
        border-color: #007bff;
    }

    .large-play-btn i {
        margin-left: 3px; /* Slight offset for visual centering */
    }

    /* Animations for resume notification */
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }

    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
    }

    #lesson-summary{
        margin-top: 12px!important;
        font-size:15px;
        background: #f2f2f2;
        border-radius: 8px;
        padding: 13px!important;
        margin: 0;
        width:100%!important;
    }
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <h2 class="mb-5">My Course | <b>Modules </b></h2>

            <!-- Check if $video_data is not empty -->
            <?php if (!empty($video_data) && isset($video_data[0])): ?>
                <h3 id="lesson-title" class="mb-3 text-center"><b><?= esc($video_data[0]['title']) ?></b></h3>
                    <div class="video-container">
                        <video id="videoPlayer" class="video-player" preload="metadata">
                            Your browser does not support the video tag.
                        </video>
                        
                        <!-- Moving Watermark -->
                        <div id="watermark" class="watermark">
                            <?= $user_details['name'] ?> | <?= $user_details['country_code'] ?>  <?= $user_details['phone'] ?>
                        </div>
                        
                        <!-- Loading Spinner -->
                        <div id="loadingSpinner" class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>

                        <!-- Large Play Button Overlay -->
                        <div id="playOverlay" class="play-overlay" style="display: none;" onclick="window.videoPlayer?.playVideo()">
                            <button class="large-play-btn">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                        
                        <!-- Custom Controls -->
                        <div class="custom-controls">
                            <!-- Progress Bar -->
                            <div class="progress-container" id="progressContainer">
                                <div class="progress-buffer" id="progressBuffer"></div>
                                <div class="progress-bar" id="progressBar"></div>
                            </div>
                            
                            <!-- Control Buttons -->
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <button class="control-btn" id="playPauseBtn">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="control-btn" id="muteBtn">
                                        <i class="fas fa-volume-up"></i>
                                    </button>
                                    <input type="range" id="volumeSlider" min="0" max="1" step="0.1" value="1" class="ms-2" style="width: 80px;">
                                    <span class="time-display" id="timeDisplay">0:00 / 0:00</span>
                                </div>
                                
                                <div class="d-flex align-items-center">
                                    <select id="qualitySelector" class="quality-selector me-2">
                                        <!-- Options will be populated dynamically by JavaScript -->
                                    </select>
                                    <button class="control-btn" id="fullscreenBtn">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                <div class="d-flex align-items-center justify-content-between">
                    <div class="w-100 row">
                        <div class="col-xl-12 col-sm-12">
                            <p id="lesson-summary" class="p-2"><?= esc($video_data[0]['summary']) ?></p>
                            <p id="lesson-duration"><?= esc($video_data[0]['duration']) ?></p>
                        </div>
                        <div class="d-flex align-items-center justify-content-between col-xl-4  col-sm-12">
                            <div class="course-item d-none">
                                <a href="<?= base_url('app/materials/index/' . $lesson_id) ?>" target="_blank">
                                    <div class="rounded-pill border border-dark-subtle border-1 px-2">
                                        <i class="ri-file-copy-line fs-1"></i>
                                    </div>
                                </a>
                                <p class="text-center mt-2">Materials</p>
                            </div>
                            <div class="course-item d-none">
                                <a href="#">
                                    <div class="rounded-circle border border-dark-subtle px-2 border-1">
                                        <i class="ri-share-forward-line fs-1"></i>
                                    </div>
                                </a>
                                <p class="text-center mt-2">Share</p>
                            </div>
                            <div class="course-item d-none">
                                <a id="whatsapp-link" href="#" target="_blank">
                                    <div class="rounded-circle border border-dark-subtle px-2 border-1">
                                        <i class="ri-whatsapp-line fs-1"></i>
                                    </div>
                                </a>
                                <p class="text-center mt-2">Doubts</p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <p>No video data available</p>
            <?php endif; ?>

            <!-- Comments Section -->
            <div class="comments-section">
                <h4>Discussion</h4>

                <!-- Comment Form -->
                <div class="comment-form">
                    <form id="commentForm">
                        <textarea id="commentText" placeholder="Add your comment or question about this video..." required></textarea>
                        <button type="submit">Post Comment</button>
                        <input type="hidden" id="currentVideoId" name="video_id" value="">
                    </form>
                </div>

                <!-- Comments List -->
                <div class="comments-list" id="commentsList">
                    <div class="no-comments">Loading comments...</div>
                </div>
            </div>
        </div>

        <!-- Video Playlist Section -->
        <div class="col-md-4 mt-5">
            <div>
                <h2 class=" m-0 py-2 px-4">Videos</h2>
                <ul id="playlist">
                    <?php foreach ($video_data as $key => $video): ?>
                        <li data-lesson-id="<?= esc($video['lesson_id']) ?>"
                            data-video-id="<?= esc($video['id'] ?? basename($video['video_url'])) ?>"
                            class="<?= $key === 0 ? 'active' : '' ?> rounded-3">
                            <i class="ri-play-circle-fill fs-1 text-white"></i>
                            <div class="w-75 ms-2"><?= esc($video['title']) ?> </div>
                            <div class="text-end"><?= esc($video['duration']) ?></div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div>
                <h2 class="m-0 py-2 px-4">Files</h2>
                <ul id="playlist">
                    <?php foreach ($file_data as $file): 
                        $file_url = base_url(get_file($file['attachment']));
                        $file_ext = pathinfo($file['attachment'], PATHINFO_EXTENSION);
                        
                        // Determine the correct viewer
                        if ($file_ext == 'pdf') {
                            $view_url = $file_url; // Open PDF directly
                        } elseif (in_array($file_ext, ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'])) {
                            $view_url = "https://view.officeapps.live.com/op/view.aspx?src=" . urlencode($file_url);
                        } else {
                            $view_url = "#"; // Fallback for unsupported types
                        }
                    ?>
                        <li class="rounded-3 d-flex align-items-center gap-3">
                            <span><?= $file['title'] ?></span>
                            <a href="<?= $view_url ?>" target="_blank">
                                <button class="btn btn-primary">View</button>
                            </a>
                            <a href="<?= base_url('download_file?item=' . encode_file($file['attachment'])) ?>" download="<?= $file['title'] ?>" class="btn btn-success">Download</a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Initialize the playlist
        const playlistItems = document.querySelectorAll('#playlist li');
        const whatsappLink = document.getElementById('whatsapp-link');
        const whatsappNumber = '<?= $doubt_number ?>';

        // Function to generate WhatsApp link
        function generateWhatsAppLink(lessonTitle) {
            const message = `I have a doubt in: ${lessonTitle}`;
            return `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
        }

        // Function to load and play video
        function loadAndPlayVideo(item) {
            const videoId = item.getAttribute('data-video-id');
            const lessonId = item.getAttribute('data-lesson-id');
            window.location.href = '<?= base_url('app/lessons/videos/') ?>' + lessonId + '/' + videoId;
        }

        function loadComments(videoId) {
            $.ajax({
                url: '<?= base_url('app/lessons/get_comment_by_video_id') ?>',
                type: 'GET',
                data: {
                    video_id: videoId
                },
                success: function(response) {
                    if (response.success) {
                        // console.log(response.comments)
                        renderComments(response.comments);
                    } else {
                        document.getElementById('commentsList').innerHTML = '<div class="no-comments">No comments yet. Be the first to comment!</div>';
                    }
                },
                error: function() {
                    document.getElementById('commentsList').innerHTML = '<div class="no-comments">Failed to load comments.</div>';
                }
            });
        }

        // Function to render comments
        function renderComments(comments) {
            const commentsList = document.getElementById('commentsList');

            if (!comments || comments.length === 0) {
                commentsList.innerHTML = '<div class="no-comments">No comments yet. Be the first to comment!</div>';
                return;
            }

            let commentsHTML = '';
            comments.forEach(comment => {
                commentsHTML += `
                    <div class="comment">
                        <div class="comment-header">
                            <span class="comment-author">${comment.user}</span>
                            <span class="comment-date">${comment.formatted_created_at}</span>
                        </div>
                        <div class="comment-content">
                            ${comment.comment}
                        </div>
                    </div>
                `;
            });

            commentsList.innerHTML = commentsHTML;
        }

        // Event listener for video clicks
        playlistItems.forEach(item => {
            item.addEventListener('click', function () {
                loadAndPlayVideo(this);
            });
        });
        
        document.getElementById('commentForm').addEventListener('submit', function (e) {
            e.preventDefault();
        
            const comment = document.getElementById('commentText').value;
            const videoId = document.getElementById('currentVideoId').value;
        
            if (!comment || !videoId) {
                alert('Comment or video ID missing!');
                return;
            }
        
            $.ajax({
                url: '<?= base_url('app/lessons/submit_comment') ?>',
                method: 'POST',
                data: {
                    video_id: videoId,
                    comment: comment
                },
                success: function (response) {
                    if (response.success) {
                        // Clear the comment input
                        document.getElementById('commentText').value = '';
                        // Reload comments
                        loadComments(videoId);
                    } else {
                        alert('Failed to post comment. Please try again.');
                    }
                },
                error: function () {
                    alert('Error occurred. Please try again.');
                }
            });
        });

    </script>

<script>
    class CustomVideoPlayer {
        constructor() {
            this.video = document.getElementById('videoPlayer');
            this.playPauseBtn = document.getElementById('playPauseBtn');
            this.muteBtn = document.getElementById('muteBtn');
            this.volumeSlider = document.getElementById('volumeSlider');
            this.progressContainer = document.getElementById('progressContainer');
            this.progressBar = document.getElementById('progressBar');
            this.progressBuffer = document.getElementById('progressBuffer');
            this.timeDisplay = document.getElementById('timeDisplay');
            this.qualitySelector = document.getElementById('qualitySelector');
            this.fullscreenBtn = document.getElementById('fullscreenBtn');
            this.watermark = document.getElementById('watermark');
            this.loadingSpinner = document.getElementById('loadingSpinner');
            this.playOverlay = document.getElementById('playOverlay');
            
            // Video sources will be populated from PHP data
            this.videoSources = {};
            
            this.currentQuality = '720';
            this.progressUpdateCount = 0;
            this.watermarkPositions = [
                { top: '40px', left: '40px' },
                { top: '40px', right: '40px' },
                { bottom: '280px', right: '60px' },
                { bottom: '380px', left: '90px' },
                { bottom: '120px', left: '70px' },
                { bottom: '180px', left: '50px' },
                { bottom: '280px', left: '150px' },
                { bottom: '240px', left: '350px' },
            ];
            this.currentWatermarkIndex = 0;
            
            this.init();
        }
        
        init() {
            // Set initial video properties
            this.video.muted = false;
            this.video.volume = 0.8;
            this.progressLoaded = false; // Flag to prevent multiple progress loads

            // Load video data from PHP
            this.loadVideoFromData();
            this.bindEvents();
            this.startWatermarkMovement();
            this.startProgressTracking();

            // Show play overlay initially
            this.showPlayOverlay();
        }

        showPlayOverlay() {
            if (this.playOverlay) {
                this.playOverlay.style.display = 'flex';
            }
        }

        hidePlayOverlay() {
            if (this.playOverlay) {
                this.playOverlay.style.display = 'none';
            }
        }

        playVideo() {
            this.video.play().then(() => {
                console.log('Video started playing from play overlay');
                this.hidePlayOverlay();
                this.playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            }).catch(e => {
                console.error('Failed to play video:', e);
            });
        }

        loadVideoFromData() {
            // Get the active video data from PHP
            <?php if (!empty($active_video)): ?>
                const activeVideo = <?= json_encode($active_video) ?>;
                console.log('Active video data:', activeVideo);

                if (activeVideo && activeVideo.video_links && Array.isArray(activeVideo.video_links)) {
                    // Convert video_links array to quality-keyed object
                    this.videoSources = {};
                    const availableQualities = [];

                    activeVideo.video_links.forEach(link => {
                        if (link.quality && link.link) {
                            this.videoSources[link.quality] = link.link;
                            // Only add if not already in the array (prevent duplicates)
                            if (!availableQualities.includes(link.quality)) {
                                availableQualities.push(link.quality);
                            }
                        }
                    });

                    console.log('Video sources:', this.videoSources);
                    console.log('Available qualities (no duplicates):', availableQualities);

                    if (availableQualities.length > 0) {
                        // Set default quality based on available sources - check for 720p or 720
                        const has720 = availableQualities.some(q => q === '720' || q === '720p');
                        this.currentQuality = has720 ? (availableQualities.includes('720') ? '720' : '720p') : availableQualities[0];
                        console.log('Selected default quality:', this.currentQuality);

                        // Update quality selector options
                        this.updateQualityOptions(availableQualities);

                        // Load the video
                        this.loadVideo(this.currentQuality);
                    } else {
                        console.error('No valid video sources found');
                        this.showLoading(false);
                    }
                } else {
                    console.error('No video_links found in active video data');
                    this.showLoading(false);
                }
            <?php else: ?>
                // Fallback: try to use the first video from video_data
                <?php if (!empty($video_data) && isset($video_data[0])): ?>
                    const firstVideo = <?= json_encode($video_data[0]) ?>;
                    console.log('Using first video as fallback:', firstVideo);

                    if (firstVideo && firstVideo.video_links && Array.isArray(firstVideo.video_links)) {
                        // Convert video_links array to quality-keyed object
                        this.videoSources = {};
                        const availableQualities = [];

                        firstVideo.video_links.forEach(link => {
                            if (link.quality && link.link) {
                                this.videoSources[link.quality] = link.link;
                                // Only add if not already in the array (prevent duplicates)
                                if (!availableQualities.includes(link.quality)) {
                                    availableQualities.push(link.quality);
                                }
                            }
                        });

                        if (availableQualities.length > 0) {
                            // Set default quality based on available sources - check for 720p or 720
                            const has720 = availableQualities.some(q => q === '720' || q === '720p');
                            this.currentQuality = has720 ? (availableQualities.includes('720') ? '720' : '720p') : availableQualities[0];
                            console.log('Fallback selected default quality:', this.currentQuality);
                            this.updateQualityOptions(availableQualities);
                            this.loadVideo(this.currentQuality);
                        } else {
                            console.error('No valid video sources found in first video');
                            this.showLoading(false);
                        }
                    } else {
                        console.error('First video has no valid video_links');
                        this.showLoading(false);
                    }
                <?php else: ?>
                    console.error('No video data available at all');
                    this.showLoading(false);
                <?php endif; ?>
            <?php endif; ?>
        }

        updateQualityOptions(availableQualities) {
            const qualitySelector = this.qualitySelector;
            // Clear all existing options
            qualitySelector.innerHTML = '';

            const qualityLabels = {
                '1080': '1080p HD',
                '1080p': '1080p HD',
                '720': '720p HD',
                '720p': '720p HD',
                '540': '540p',
                '540p': '540p',
                '360': '360p',
                '360p': '360p',
                '240': '240p',
                '240p': '240p'
            };

            // Sort qualities to prioritize 720p, then by numeric value
            const sortedQualities = [...availableQualities].sort((a, b) => {
                // Extract numeric values
                const aNum = parseInt(a.replace('p', ''));
                const bNum = parseInt(b.replace('p', ''));

                // Prioritize 720p
                if (aNum === 720) return -1;
                if (bNum === 720) return 1;

                // Then sort by descending quality
                return bNum - aNum;
            });

            console.log('Sorted qualities:', sortedQualities);

            sortedQualities.forEach(quality => {
                const option = document.createElement('option');
                option.value = quality;
                // Use predefined label or format the quality value properly
                option.textContent = qualityLabels[quality] || (quality.includes('p') ? quality : quality + 'p');

                // Set as selected if it's the current quality
                if (quality === this.currentQuality) {
                    option.selected = true;
                }

                qualitySelector.appendChild(option);
            });

            // Ensure the selector shows the current quality
            qualitySelector.value = this.currentQuality;
            console.log('Quality selector updated. Current quality:', this.currentQuality);
        }
        
        loadVideo(quality) {
            if (!this.videoSources || !this.videoSources[quality]) {
                console.error('Video source not available for quality:', quality);
                this.showLoading(false);
                return;
            }

            this.showLoading(true);
            const currentTime = this.video.currentTime || 0;
            const wasPlaying = !this.video.paused;

            this.video.src = this.videoSources[quality];
            this.currentQuality = quality;

            // Add error handling
            this.video.addEventListener('error', (e) => {
                console.error('Video loading error:', e);
                this.showLoading(false);
            }, { once: true });

            this.video.addEventListener('loadeddata', () => {
                // Restore the previous time position (for quality changes)
                if (currentTime > 0) {
                    this.video.currentTime = currentTime;
                }

                if (wasPlaying) {
                    // Continue playing if video was already playing
                    this.video.play().then(() => {
                        console.log('Video resumed playing after quality change');
                        this.playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                        this.hidePlayOverlay();
                    }).catch(e => {
                        console.error('Failed to resume video:', e);
                        this.playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                        this.showPlayOverlay();
                    });
                } else {
                    // Video is paused, show play overlay
                    this.playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                    this.showPlayOverlay();
                }

                this.showLoading(false);
                this.updateQualityStats();
            }, { once: true });

            this.video.load();
        }
        
        bindEvents() {
            // Play/Pause
            this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
            this.video.addEventListener('click', () => this.togglePlayPause());
            
            // Mute/Unmute
            this.muteBtn.addEventListener('click', () => this.toggleMute());
            
            // Volume
            this.volumeSlider.addEventListener('input', (e) => {
                this.video.volume = e.target.value;
                this.updateVolumeIcon();
            });
            
            // Progress
            this.progressContainer.addEventListener('click', (e) => this.seek(e));
            this.video.addEventListener('timeupdate', () => this.updateProgress());
            this.video.addEventListener('progress', () => this.updateBuffer());
            
            // Quality change
            this.qualitySelector.addEventListener('change', (e) => {
                this.loadVideo(e.target.value);
            });
            
            // Fullscreen
            this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());

            // Fullscreen change events
            document.addEventListener('fullscreenchange', () => this.handleFullscreenChange());
            document.addEventListener('webkitfullscreenchange', () => this.handleFullscreenChange());
            document.addEventListener('mozfullscreenchange', () => this.handleFullscreenChange());
            document.addEventListener('MSFullscreenChange', () => this.handleFullscreenChange());

            // Video events
            this.video.addEventListener('loadstart', () => this.showLoading(true));
            this.video.addEventListener('canplay', () => this.showLoading(false));
            this.video.addEventListener('waiting', () => this.showLoading(true));
            this.video.addEventListener('playing', () => {
                this.showLoading(false);
                this.hidePlayOverlay();
                this.playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                this.updateControlsVisibility(false); // Video is playing
            });
            this.video.addEventListener('pause', () => {
                this.showPlayOverlay();
                this.playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                this.updateControlsVisibility(true); // Video is paused
            });
            this.video.addEventListener('ended', () => {
                this.showPlayOverlay();
                this.playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                this.updateControlsVisibility(true); // Video ended (paused state)
            });

            // Keyboard controls
            document.addEventListener('keydown', (e) => this.handleKeyboard(e));
        }
        
        togglePlayPause() {
            if (this.video.paused) {
                this.video.play().then(() => {
                    this.playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                    this.hidePlayOverlay();
                }).catch(e => {
                    console.error('Failed to play video:', e);
                });
            } else {
                this.video.pause();
                this.playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                this.showPlayOverlay();
            }
        }
        
        toggleMute() {
            this.video.muted = !this.video.muted;
            this.updateVolumeIcon();
        }
        
        updateVolumeIcon() {
            const icon = this.muteBtn.querySelector('i');
            if (this.video.muted || this.video.volume === 0) {
                icon.className = 'fas fa-volume-mute';
            } else if (this.video.volume < 0.5) {
                icon.className = 'fas fa-volume-down';
            } else {
                icon.className = 'fas fa-volume-up';
            }
        }
        
        seek(e) {
            const percent = e.offsetX / this.progressContainer.offsetWidth;
            this.video.currentTime = percent * this.video.duration;
        }
        
        updateProgress() {
            if (this.video.duration) {
                const percent = (this.video.currentTime / this.video.duration) * 100;
                this.progressBar.style.width = percent + '%';
                
                // Update time display
                const current = this.formatTime(this.video.currentTime);
                const duration = this.formatTime(this.video.duration);
                this.timeDisplay.textContent = `${current} / ${duration}`;
            }
        }
        
        updateBuffer() {
            if (this.video.buffered.length > 0) {
                const percent = (this.video.buffered.end(0) / this.video.duration) * 100;
                this.progressBuffer.style.width = percent + '%';
            }
        }
        
        formatTime(seconds) {
            // Ensure we're working with integer seconds (no milliseconds)
            const totalSeconds = Math.floor(seconds);
            const mins = Math.floor(totalSeconds / 60);
            const secs = totalSeconds % 60;
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }
        
        toggleFullscreen() {
            const container = document.querySelector('.video-container');

            if (!this.isFullscreen()) {
                // Enter fullscreen
                if (container.requestFullscreen) {
                    container.requestFullscreen();
                } else if (container.webkitRequestFullscreen) {
                    container.webkitRequestFullscreen();
                } else if (container.mozRequestFullScreen) {
                    container.mozRequestFullScreen();
                } else if (container.msRequestFullscreen) {
                    container.msRequestFullscreen();
                }
            } else {
                // Exit fullscreen
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        }

        isFullscreen() {
            return !!(document.fullscreenElement || document.webkitFullscreenElement ||
                     document.mozFullScreenElement || document.msFullscreenElement);
        }

        handleFullscreenChange() {
            if (this.isFullscreen()) {
                this.fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
            } else {
                this.fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
            }
        }
        
        handleKeyboard(e) {
            if (e.target.tagName.toLowerCase() === 'input') return;
            
            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlayPause();
                    break;
                case 'KeyM':
                    this.toggleMute();
                    break;
                case 'KeyF':
                    this.toggleFullscreen();
                    break;
                case 'ArrowLeft':
                    this.video.currentTime -= 10;
                    break;
                case 'ArrowRight':
                    this.video.currentTime += 10;
                    break;
            }
        }
        
        startWatermarkMovement() {
            setInterval(() => {
                this.currentWatermarkIndex = (this.currentWatermarkIndex + 1) % this.watermarkPositions.length;
                const position = this.watermarkPositions[this.currentWatermarkIndex];
                
                // Reset all position properties
                this.watermark.style.top = 'auto';
                this.watermark.style.bottom = 'auto';
                this.watermark.style.left = 'auto';
                this.watermark.style.right = 'auto';
                
                // Apply new position
                Object.keys(position).forEach(key => {
                    this.watermark.style[key] = position[key];
                });
            }, 5000); // Move every 5 seconds
        }
        
        startProgressTracking() {
            // Initialize progress tracking variables
            this.lastSavedTime = 0;
            this.lastSavedAt = 0;
            this.progressSaveQueue = [];
            this.saveInProgress = false;

            // Load existing progress when video metadata is loaded
            this.video.addEventListener('loadedmetadata', () => {
                this.loadVideoProgress();
            });

            // Also try to load progress when video can play (fallback)
            this.video.addEventListener('canplay', () => {
                if (!this.progressLoaded) {
                    this.loadVideoProgress();
                }
            });

            // Optimized progress saving - less frequent but smarter
            setInterval(() => {
                this.checkAndSaveProgress();
            }, 30000); // Reduced frequency: every 30 seconds instead of 10

            // Save progress on important events
            this.video.addEventListener('pause', () => {
                this.saveProgressToAPI(true); // Force save on pause
            });

            this.video.addEventListener('ended', () => {
                this.saveProgressToAPI(true); // Force save on end
            });

            // Save progress when user is about to leave the page
            window.addEventListener('beforeunload', () => {
                this.saveProgressToAPI(true, true); // Force save synchronously
            });

            // Save progress when page becomes hidden (tab switch, minimize)
            document.addEventListener('visibilitychange', () => {
                if (document.hidden && !this.video.paused) {
                    this.saveProgressToAPI(true);
                }
            });
        }

        async loadVideoProgress() {
            try {
                // Prevent multiple loads
                if (this.progressLoaded) {
                    return;
                }

                const lessonFileId = this.getCurrentLessonFileId();
                if (!lessonFileId) {
                    console.log('No lesson file ID available for progress loading');
                    return;
                }

                // Ensure video has duration before trying to set currentTime
                if (!this.video.duration) {
                    console.log('Video duration not available yet, waiting...');
                    return;
                }

                console.log('Loading video progress for lesson file ID:', lessonFileId);

                const response = await fetch(`<?= base_url('app/lessons/get_video_progress') ?>?lesson_file_id=${lessonFileId}`);
                const result = await response.json();

                console.log('Progress API response:', result);

                if (result.success && result.data && result.data.progress_seconds > 0) {
                    // Only resume if progress is meaningful (more than 5 seconds and less than 95% complete)
                    const progressSeconds = result.data.progress_seconds;
                    const progressRate = result.data.progress_rate || 0;

                    if (progressSeconds >= 5 && progressRate < 95) {
                        // Resume from saved position
                        this.video.currentTime = progressSeconds;
                        console.log(`✅ Resumed video from ${result.data.user_progress} (${progressRate}% complete)`);

                        // Show a brief notification
                        this.showResumeNotification(result.data.user_progress, progressRate);
                    } else if (progressRate >= 95) {
                        console.log(`Video was ${progressRate}% complete, starting from beginning`);
                    } else {
                        console.log(`Progress too small (${progressSeconds}s), starting from beginning`);
                    }
                } else {
                    console.log('No previous progress found, starting from beginning');
                }

                this.progressLoaded = true;

            } catch (error) {
                console.error('Failed to load video progress:', error);
                this.progressLoaded = true; // Prevent retries
            }
        }

        checkAndSaveProgress() {
            // Only save if video is playing and has meaningful progress
            if (this.video.paused || !this.video.duration || this.video.currentTime <= 0) {
                return;
            }

            const currentTime = Math.floor(this.video.currentTime);
            const timeSinceLastSave = Date.now() - this.lastSavedAt;
            const progressDifference = Math.abs(currentTime - this.lastSavedTime);

            // Smart saving conditions:
            // 1. At least 30 seconds have passed since last save
            // 2. Progress has changed by at least 15 seconds
            // 3. Haven't saved in the last 5 seconds (prevent spam)
            if (timeSinceLastSave >= 30000 && progressDifference >= 15 && timeSinceLastSave >= 5000) {
                this.saveProgressToAPI();
            }
        }

        async saveProgressToAPI(forceSave = false, synchronous = false) {
            try {
                // Prevent concurrent saves unless forced
                if (this.saveInProgress && !forceSave) {
                    console.log('Save already in progress, skipping...');
                    return;
                }

                const lessonFileId = this.getCurrentLessonFileId();
                const lessonId = this.getCurrentLessonId();

                if (!lessonFileId || !lessonId || !this.video.duration) {
                    return;
                }

                const currentTime = Math.floor(this.video.currentTime);
                const totalDuration = Math.floor(this.video.duration);

                // Skip if no meaningful change (unless forced)
                if (!forceSave && Math.abs(currentTime - this.lastSavedTime) < 10) {
                    console.log('Progress change too small, skipping save');
                    return;
                }

                this.saveInProgress = true;

                const progressData = {
                    lesson_id: lessonId,
                    lesson_file_id: lessonFileId,
                    current_time: currentTime,
                    total_duration: totalDuration
                };

                const fetchOptions = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(progressData)
                };

                // Use synchronous request for page unload
                if (synchronous) {
                    navigator.sendBeacon('<?= base_url('app/lessons/save_video_progress') ?>',
                        new URLSearchParams(progressData));
                    console.log('Progress saved synchronously on page unload');
                    return;
                }

                const response = await fetch('<?= base_url('app/lessons/save_video_progress') ?>', fetchOptions);
                const result = await response.json();

                if (result.success) {
                    this.progressUpdateCount++;
                    this.lastSavedTime = currentTime;
                    this.lastSavedAt = Date.now();

                    console.log(`Progress saved: ${result.data.progress_rate}% complete (${this.formatTimeForLogging(currentTime)})`);

                    // Update UI if needed
                    this.updateProgressDisplay(result.data);
                } else {
                    console.error('Failed to save progress:', result.message);
                }

            } catch (error) {
                console.error('Failed to send progress to API:', error);
            } finally {
                this.saveInProgress = false;
            }
        }

        formatTimeForLogging(seconds) {
            // Format time for console logging (can include hours if needed)
            const totalSeconds = Math.floor(seconds);
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const secs = totalSeconds % 60;

            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }

        getCurrentLessonFileId() {
            // Get from active video data or current video ID
            <?php if (!empty($active_video)): ?>
                return '<?= $active_video['id'] ?? '' ?>';
            <?php elseif (!empty($video_data) && isset($video_data[0])): ?>
                return '<?= $video_data[0]['id'] ?? '' ?>';
            <?php else: ?>
                return null;
            <?php endif; ?>
        }

        getCurrentLessonId() {
            return '<?= $lesson_id ?? '' ?>';
        }

        updateProgressDisplay(progressData) {
            // You can add UI updates here if needed
            // For example, update a progress bar or completion indicator
            if (progressData.status === 1) {
                console.log('Video completed!');
                // Could show completion badge or update UI
            }
        }

        showResumeNotification(timeString, progressRate) {
            // Create a resume notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: absolute;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 123, 255, 0.9);
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                z-index: 1001;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                animation: slideDown 0.3s ease-out;
            `;
            notification.innerHTML = `
                <i class="fas fa-play-circle" style="margin-right: 8px;"></i>
                Resumed from ${timeString} (${progressRate}% watched)
            `;

            const container = document.querySelector('.video-container');
            container.appendChild(notification);

            // Auto-remove after 4 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideUp 0.3s ease-in';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }
            }, 4000);
        }
        
        showLoading(show) {
            if (show) {
                this.loadingSpinner.classList.add('show');
                this.loadingSpinner.style.display = 'block';
            } else {
                this.loadingSpinner.classList.remove('show');
                this.loadingSpinner.style.display = 'none';
            }
        }
        
        updateQualityStats() {
            // document.getElementById('qualityStats').textContent = this.currentQuality + 'p';
            this.qualitySelector.value = this.currentQuality;
        }
    }
    
    // Debug: Log available data
    console.log('=== VIDEO PLAYER DEBUG INFO ===');
    <?php if (!empty($active_video)): ?>
        console.log('Active video available:', <?= json_encode($active_video) ?>);
    <?php else: ?>
        console.log('No active video available');
    <?php endif; ?>

    <?php if (!empty($video_data)): ?>
        console.log('Video data available:', <?= json_encode($video_data) ?>);
    <?php else: ?>
        console.log('No video data available');
    <?php endif; ?>
    console.log('=== END DEBUG INFO ===');

    // Initialize the video player when the page loads
    document.addEventListener('DOMContentLoaded', () => {
        try {
            window.videoPlayer = new CustomVideoPlayer();
            console.log('Video player initialized successfully');
        } catch (error) {
            console.error('Failed to initialize video player:', error);
        }
    });

    // Set current video ID for comments
    document.addEventListener('DOMContentLoaded', () => {
        let currentVideoId = '';

        <?php if (!empty($active_video)): ?>
            currentVideoId = '<?= $active_video['id'] ?? '' ?>';
        <?php elseif (!empty($video_data) && isset($video_data[0])): ?>
            currentVideoId = '<?= $video_data[0]['id'] ?? '' ?>';
        <?php endif; ?>

        if (currentVideoId) {
            document.getElementById('currentVideoId').value = currentVideoId;
            // Load comments for the current video
            loadComments(currentVideoId);
        }
    });
</script>
</div>
