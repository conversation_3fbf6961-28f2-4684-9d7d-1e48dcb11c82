<?php

if (!function_exists('get_vimeo_file_url')) {
    function get_vimeo_file_url($vimeo_url) {
    
        $accessToken = get_settings('vimeo_access_token');
    
        if (preg_match('/vimeo\.com\/(\d+)/', $vimeo_url, $matches)) {
            $vimeoVideoId = $matches[1];
        } else {
            return [
                'status' => 'false',
                'message' => 'Invalid Vimeo URL',
                'video_link' => null
            ];
        }
    
        $vimeoApiUrl = "https://api.vimeo.com/videos/$vimeoVideoId";
    
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $vimeoApiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Authorization: Bearer $accessToken"
        ));
        $response = curl_exec($ch);
        curl_close($ch);
    
        $videoInfo = json_decode($response, true);
        
        $files      = [];
        $downloads  = [];
    
        if (isset($videoInfo['files'])) 
        {
            $files      =   $videoInfo['files'];
            $downloads  =   $videoInfo['download'];
            
            
            return [
                'status' => 'false',
                'message' => 'Detailed fetched for this video',
                'files' => $files,
                'downloads' => $downloads
            ];
        } 
        else 
        {
            return [
                'status' => 'false',
                'message' => 'Error fetching video details or invalid video ID',
                'files' => $files,
                'downloads' => $downloads
            ];
        }
    }
}


if (!function_exists('get_vimeo_video_url')) {
    function get_vimeo_video_url($vimeo_url) {
    
        $accessToken = get_settings('vimeo_access_token');
    
        if (preg_match('/vimeo\.com\/(\d+)/', $vimeo_url, $matches)) {
            $vimeoVideoId = $matches[1];
        } else {
            return [
                'status' => 'false',
                'message' => 'Invalid Vimeo URL',
                'video_link' => null
            ];
        }
    
        $vimeoApiUrl = "https://api.vimeo.com/videos/$vimeoVideoId";
    
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $vimeoApiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Authorization: Bearer $accessToken"
        ));
        $response = curl_exec($ch);
        curl_close($ch);
    
        $videoInfo = json_decode($response, true);
    
        if (isset($videoInfo['files'])) {
            
            foreach ($videoInfo['files'] as $file) {
                
                if ($file['quality'] === 'hls') {
                    return [
                        'status' => 'true',
                        'message' => 'HLS link fetched successfully',
                        'video_link' => $file['link']
                    ];
                }
            }
            
            return [
                'status' => 'false',
                'message' => 'No HLS link found for this video',
                'video_link' => null
            ];
        } else {
            return [
                'status' => 'false',
                'message' => 'Error fetching video details or invalid video ID',
                'video_link' => null
            ];
        }
    }
    if (!function_exists('get_vimeo_details')) {
        function get_vimeo_details($vimeo_url) {
            $video_details = file_get_contents('https://vimeo.com/api/oembed.json?url=' . urlencode($vimeo_url));
            $video_details = json_decode($video_details, true);
            $video['duration'] = isset($data['duration']) ? gmdate('H:i:s', $data['duration']) : null;
            $video['thumbnail_url'] = $video_details['thumbnail_url'] ?? '';
            $video['thumbnail_url_with_play_button'] = $video_details['thumbnail_url_with_play_button'] ?? '';
            return $video;
        }
    }
}